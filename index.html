<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演讲技能分析 - 交互式气泡图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow: hidden;
        }

        .container {
            position: relative;
            width: 100vw;
            height: 100vh;
            padding: 20px;
        }

        .bubble {
            position: absolute;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(4px);
            border: 2px solid rgba(255,255,255,0.2);
        }

        .bubble:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 40px rgba(0,0,0,0.2);
        }

        .bubble-content {
            text-align: center;
            color: white;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            padding: 5px;
        }

        .bubble-title {
            font-size: 16px;
            margin-bottom: 5px;
        }

        .bubble-score {
            font-size: 20px;
            opacity: 0.9;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.7);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            margin: 15% auto;
            padding: 30px;
            border-radius: 20px;
            width: 400px;
            max-width: 90%;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-title {
            font-size: 24px;
            margin-bottom: 20px;
            color: #333;
            font-weight: bold;
        }

        .modal-message {
            font-size: 18px;
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .close {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .close:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .improve-tips {
            background-color: #f8f9fa;
            border-left: 4px solid #ff6b6b;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 10px 10px 0;
            text-align: left;
        }

        .great-job {
            background-color: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 10px 10px 0;
            text-align: left;
        }


    </style>
</head>
<body>
    <div class="container">


        <!-- 模态框 -->
        <div id="modal" class="modal">
            <div class="modal-content">
                <div id="modal-title" class="modal-title"></div>
                <div id="modal-message" class="modal-message"></div>
                <button class="close" onclick="closeModal()">关闭</button>
            </div>
        </div>
    </div>

    <script>
        // 演讲技能数据（根据图1）
        const speechData = [
            { name: 'Empathy', score: 5, x: 40, y: 50 },
            { name: 'Persuasiveness', score: 30, x: 75, y: 60 },
            { name: 'Facial Expression', score: 60, x: 55, y: 25 },
            { name: 'Enthusiasm', score: 70, x: 25, y: 70 },
            { name: 'Filter Words', score: 80, x: 72, y: 35 },
            { name: 'Pacing', score: 85, x: 80, y: 75 },
            { name: 'Logical Transitions', score: 90, x: 20, y: 40 },
            { name: 'Eye Contact', score: 95, x: 60, y: 80 },
            { name: 'Gestures', score: 95, x: 85, y: 20 },
            { name: 'Sentiment', score: 95, x: 90, y: 50 }
        ];



        // 生成气泡颜色
        function getBubbleColor(score) {
            if (score <= 30) return 'rgba(255, 107, 107, 0.8)'; // 红色
            if (score <= 60) return 'rgba(255, 165, 0, 0.8)';   // 橙色
            if (score <= 80) return 'rgba(255, 193, 7, 0.8)';   // 黄色
            return 'rgba(76, 175, 80, 0.8)';                    // 绿色
        }

        // 计算气泡大小（数值越低气泡越大）
        function getBubbleSize(score) {
            const minSize = 35;
            const maxSize = 400;
            // 分数越低，气泡越大
            return maxSize - ((score / 100) * (maxSize - minSize));
        }

        // 计算相切位置
        function calculateTangentPositions() {
            const sortedData = [...speechData].sort((a, b) => a.score - b.score);
            const positions = [];
            const containerWidth = window.innerWidth;
            const containerHeight = window.innerHeight;
            
            // 最大气泡放在中心
            const largestBubble = sortedData[0];
            const largestSize = getBubbleSize(largestBubble.score);
            positions.push({
                ...largestBubble,
                x: containerWidth * 0.5,
                y: containerHeight * 0.5,
                size: largestSize
            });
            
            // 其他气泡围绕最大气泡相切排列
            for (let i = 1; i < sortedData.length; i++) {
                const bubble = sortedData[i];
                const bubbleSize = getBubbleSize(bubble.score);
                
                let bestX = 0, bestY = 0;
                let minDistance = Infinity;
                
                // 尝试不同角度找到最佳位置
                for (let angle = 0; angle < 360; angle += 30) {
                    const radian = (angle * Math.PI) / 180;
                    const distance = (largestSize + bubbleSize) / 2 + 10; // 相切距离加小间隙
                    
                    const x = positions[0].x + Math.cos(radian) * distance;
                    const y = positions[0].y + Math.sin(radian) * distance;
                    
                    // 检查是否与其他气泡重叠
                    let hasCollision = false;
                    for (let j = 1; j < positions.length; j++) {
                        const otherBubble = positions[j];
                        const dx = x - otherBubble.x;
                        const dy = y - otherBubble.y;
                        const minDist = (bubbleSize + otherBubble.size) / 2 + 5;
                        
                        if (Math.sqrt(dx * dx + dy * dy) < minDist) {
                            hasCollision = true;
                            break;
                        }
                    }
                    
                    if (!hasCollision) {
                        const centerDistance = Math.sqrt((x - containerWidth/2)**2 + (y - containerHeight/2)**2);
                        if (centerDistance < minDistance) {
                            minDistance = centerDistance;
                            bestX = x;
                            bestY = y;
                        }
                    }
                }
                
                // 如果没找到合适位置，使用备选方案
                if (bestX === 0 && bestY === 0) {
                    const angle = (i * 40) % 360;
                    const radian = (angle * Math.PI) / 180;
                    const distance = (largestSize + bubbleSize) / 2 + 20 + i * 10;
                    bestX = positions[0].x + Math.cos(radian) * distance;
                    bestY = positions[0].y + Math.sin(radian) * distance;
                }
                
                positions.push({
                    ...bubble,
                    x: bestX,
                    y: bestY,
                    size: bubbleSize
                });
            }
            
            return positions;
        }

        // 创建气泡
        function createBubbles() {
            const container = document.querySelector('.container');
            const positions = calculateTangentPositions();
            
            positions.forEach((item) => {
                const bubble = document.createElement('div');
                bubble.className = 'bubble';
                
                const color = getBubbleColor(item.score);
                
                bubble.style.width = item.size + 'px';
                bubble.style.height = item.size + 'px';
                bubble.style.backgroundColor = color;
                bubble.style.left = item.x + 'px';
                bubble.style.top = item.y + 'px';
                bubble.style.transform = 'translate(-50%, -50%)';
                bubble.style.zIndex = Math.floor(item.size);
                
                bubble.innerHTML = `
                    <div class="bubble-content">
                        <div class="bubble-title">${item.name}</div>
                        <div class="bubble-score">${item.score}%</div>
                    </div>
                `;
                
                bubble.addEventListener('click', () => showModal(item));
                container.appendChild(bubble);
            });
        }

        // 显示模态框
        function showModal(item) {
            const modal = document.getElementById('modal');
            const title = document.getElementById('modal-title');
            const message = document.getElementById('modal-message');
            
            if (item.score <= 70) {
                // 需要改进的项目
                title.textContent = 'How to improve';
                message.innerHTML = `
                    <div class="improve-tips">
                        <p><strong>..</strong></p>
                    </div>
                `;
            } else {
                // 表现良好的项目
                title.textContent = 'Great job!';
                message.innerHTML = `
                    <div class="great-job">
                        <p><strong>..</strong></p>
                    </div>
                `;
            }
            
            modal.style.display = 'block';
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('modal').style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('modal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }

        // 页面加载时创建气泡
        document.addEventListener('DOMContentLoaded', createBubbles);
    </script>
</body>
</html> 